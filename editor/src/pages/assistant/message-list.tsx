import { styled } from '@topwrite/common';
import { useContext } from './context';
import MessageItem from './message-item';
import { useCallback, useEffect, useRef } from 'react';

export default function MessageList() {
    const { messages } = useContext();
    const scrollRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = useCallback(() => {
        const dom = scrollRef.current;
        if (dom) {
            // 使用多次 requestAnimationFrame 确保 DOM 完全渲染
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    // 使用 setTimeout 进一步延迟，确保异步内容也渲染完成
                    setTimeout(() => {
                        dom.scrollTo({
                            top: dom.scrollHeight
                        });
                    }, 50);
                });
            });
        }
    }, []);

    useEffect(() => {
        if (messages.length > 0) {
            scrollToBottom();
        }
    }, [messages, scrollToBottom]);

    return <Container ref={scrollRef}>
        {messages.map((message, index) => {
            return <MessageItem key={index} message={message} />;
        })}
    </Container>;
}

const Container = styled.div`
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
`;
